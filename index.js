#!/usr/bin/env node

/**
 * Frontend Development MCP Tools
 * Main entry point for the package
 */

console.log('Frontend Development MCP Tools');
console.log('For setup: npm run setup or frontend-dev-mcp-setup');
console.log('To start: npm start or frontend-dev-mcp');
console.log('For help: frontend-dev-mcp --help');

export default {
  name: '@winds-ai/frontend-development-mcp-tools',
  version: '1.2.2',
  description: 'Complete Browser MCP Tools package for frontend development with AI IDEs'
};
