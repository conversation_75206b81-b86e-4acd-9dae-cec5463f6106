{"name": "@winds-ai/frontend-development-mcp-server", "version": "1.2.0", "description": "A browser tools server for capturing and managing browser events, logs, and screenshots", "type": "module", "main": "dist/browser-connector.js", "bin": {"frontend-development-mcp-server": "./dist/browser-connector.js"}, "scripts": {"build": "tsc", "start": "node dist/browser-connector.js", "dev": "tsc && node dist/browser-connector.js", "prepublishOnly": "npm run build", "postinstall": "npm run build"}, "keywords": ["browser", "tools", "debugging", "logging", "screenshots", "chrome", "extension"], "author": "Winds AI", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Winds-AI/Frontend-development-mcp-tools"}, "inspiredBy": "https://github.com/AgentDeskAI/browser-tools-mcp", "dependencies": {"@modelcontextprotocol/sdk": "^1.4.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "lighthouse": "^11.6.0", "node-fetch": "^2.7.0", "puppeteer-core": "^22.4.1", "ws": "^8.18.0"}, "optionalDependencies": {"chrome-launcher": "^1.1.2"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/node": "^22.15.20", "@types/node-fetch": "^2.6.11", "@types/puppeteer-core": "^7.0.4", "@types/ws": "^8.18.1", "typescript": "^5.7.3"}}